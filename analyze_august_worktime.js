const fs = require('fs');

// 读取JSON文件
function readDataFile() {
    try {
        const data = fs.readFileSync('data.json', 'utf8');
        const jsonData = JSON.parse(data);
        return jsonData.data || [];
    } catch (error) {
        console.error('读取文件失败:', error);
        return [];
    }
}

// 检查日期是否为8月份
function isAugust(dateString) {
    if (!dateString) return false;
    const date = new Date(dateString);
    return date.getFullYear() === 2025 && date.getMonth() === 7; // 8月份是索引7
}

// 统计8月份每个人各个项目的用时
function analyzeAugustWorkTime() {
    const data = readDataFile();

    // 过滤8月份的数据
    const augustData = data.filter(item => {
        return isAugust(item.PlanDate) || isAugust(item.CreateDatetime);
    });

    console.log(`总共找到 ${augustData.length} 条8月份的记录`);

    // 按人员和项目分组统计
    const stats = {};

    augustData.forEach(item => {
        const userName = item.CreateUserName || '未知用户';
        const projectName = item.DevProjectName || '未知项目';
        const workTime = parseFloat(item.WorkTime) || 0;

        // 初始化用户数据
        if (!stats[userName]) {
            stats[userName] = {};
        }

        // 初始化项目数据
        if (!stats[userName][projectName]) {
            stats[userName][projectName] = {
                totalTime: 0,
                taskCount: 0,
                tasks: []
            };
        }

        // 累加工时
        stats[userName][projectName].totalTime += workTime;
        stats[userName][projectName].taskCount += 1;
        stats[userName][projectName].tasks.push({
            title: item.MatterTitle,
            workTime: workTime,
            date: item.PlanDate,
            category: item.MatterCateName
        });
    });

    return stats;
}

// 格式化输出结果
function displayResults(stats) {
    console.log('\n=== 8月份工时统计报告 ===\n');

    // 按用户排序
    const sortedUsers = Object.keys(stats).sort();

    let totalAllTime = 0;

    sortedUsers.forEach(userName => {
        console.log(`👤 ${userName}:`);
        console.log('─'.repeat(50));

        const userProjects = stats[userName];
        const sortedProjects = Object.keys(userProjects).sort();

        let userTotalTime = 0;

        sortedProjects.forEach(projectName => {
            const projectData = userProjects[projectName];
            userTotalTime += projectData.totalTime;

            console.log(`  📋 ${projectName}`);
            console.log(`     ⏱️  总工时: ${projectData.totalTime.toFixed(1)} 小时`);
            console.log(`     📝 任务数: ${projectData.taskCount} 个`);
            console.log(`     📊 平均工时: ${(projectData.totalTime / projectData.taskCount).toFixed(1)} 小时/任务`);
            console.log('');
        });

        console.log(`  🎯 ${userName} 8月总工时: ${userTotalTime.toFixed(1)} 小时`);
        console.log(`  📈 参与项目数: ${sortedProjects.length} 个`);
        console.log('');

        totalAllTime += userTotalTime;
    });

    console.log('='.repeat(60));
    console.log(`🏆 8月份全员总工时: ${totalAllTime.toFixed(1)} 小时`);
    console.log(`👥 参与人员数: ${sortedUsers.length} 人`);
    console.log(`📊 平均每人工时: ${(totalAllTime / sortedUsers.length).toFixed(1)} 小时`);
}

// 生成详细报表
function generateDetailedReport(stats) {
    console.log('\n=== 详细工时明细 ===\n');

    Object.keys(stats).sort().forEach(userName => {
        console.log(`\n📋 ${userName} 详细明细:`);
        console.log('─'.repeat(80));

        Object.keys(stats[userName]).sort().forEach(projectName => {
            const projectData = stats[userName][projectName];
            console.log(`\n  🔹 ${projectName} (${projectData.totalTime.toFixed(1)}h):`);

            projectData.tasks.forEach(task => {
                console.log(`    • ${task.title} - ${task.workTime}h [${task.category}] (${task.date.split(' ')[0]})`);
            });
        });
    });
}

// 生成项目维度统计
function generateProjectStats(stats) {
    console.log('\n=== 项目维度统计 ===\n');

    const projectStats = {};

    // 重新组织数据，按项目分组
    Object.keys(stats).forEach(userName => {
        Object.keys(stats[userName]).forEach(projectName => {
            if (!projectStats[projectName]) {
                projectStats[projectName] = {
                    totalTime: 0,
                    participants: [],
                    taskCount: 0
                };
            }

            projectStats[projectName].totalTime += stats[userName][projectName].totalTime;
            projectStats[projectName].participants.push({
                name: userName,
                time: stats[userName][projectName].totalTime
            });
            projectStats[projectName].taskCount += stats[userName][projectName].taskCount;
        });
    });

    // 按总工时排序项目
    const sortedProjects = Object.keys(projectStats).sort((a, b) =>
        projectStats[b].totalTime - projectStats[a].totalTime
    );

    sortedProjects.forEach(projectName => {
        const project = projectStats[projectName];
        console.log(`🚀 ${projectName}:`);
        console.log(`   ⏱️  总工时: ${project.totalTime.toFixed(1)} 小时`);
        console.log(`   👥 参与人数: ${project.participants.length} 人`);
        console.log(`   📝 任务总数: ${project.taskCount} 个`);
        console.log(`   👨‍💻 参与人员:`);

        project.participants
            .sort((a, b) => b.time - a.time)
            .forEach(participant => {
                console.log(`      • ${participant.name}: ${participant.time.toFixed(1)}h`);
            });
        console.log('');
    });
}

// 生成HTML报告
function generateHTMLReport(stats) {
    const projectStats = {};

    // 重新组织数据，按项目分组
    Object.keys(stats).forEach(userName => {
        Object.keys(stats[userName]).forEach(projectName => {
            if (!projectStats[projectName]) {
                projectStats[projectName] = {
                    totalTime: 0,
                    participants: [],
                    taskCount: 0
                };
            }

            projectStats[projectName].totalTime += stats[userName][projectName].totalTime;
            projectStats[projectName].participants.push({
                name: userName,
                time: stats[userName][projectName].totalTime
            });
            projectStats[projectName].taskCount += stats[userName][projectName].taskCount;
        });
    });

    // 计算总工时
    let totalAllTime = 0;
    Object.keys(stats).forEach(userName => {
        Object.keys(stats[userName]).forEach(projectName => {
            totalAllTime += stats[userName][projectName].totalTime;
        });
    });

    // 按总工时排序项目
    const sortedProjects = Object.keys(projectStats).sort((a, b) =>
        projectStats[b].totalTime - projectStats[a].totalTime
    );

    // 按用户工时排序
    const sortedUsers = Object.keys(stats).sort((a, b) => {
        const aTotal = Object.values(stats[a]).reduce((sum, project) => sum + project.totalTime, 0);
        const bTotal = Object.values(stats[b]).reduce((sum, project) => sum + project.totalTime, 0);
        return bTotal - aTotal;
    });

    const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8月份工时统计报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 40px;
            background: #f8f9fa;
        }

        .card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .card-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .card-label {
            color: #666;
            font-size: 1.1em;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 50px;
        }

        .section-title {
            font-size: 2em;
            color: #333;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: left;
            font-weight: 600;
        }

        td {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .rank-badge {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }

        .project-participants {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }

        .participant-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        @media (max-width: 768px) {
            .summary-cards {
                grid-template-columns: 1fr;
                padding: 20px;
            }

            .content {
                padding: 20px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 8月份工时统计报告</h1>
            <div class="subtitle">2025年8月团队工作量分析</div>
        </div>

        <div class="summary-cards">
            <div class="card">
                <div class="card-icon">⏱️</div>
                <div class="card-value">${totalAllTime.toFixed(1)}</div>
                <div class="card-label">总工时（小时）</div>
            </div>
            <div class="card">
                <div class="card-icon">👥</div>
                <div class="card-value">${Object.keys(stats).length}</div>
                <div class="card-label">参与人员</div>
            </div>
            <div class="card">
                <div class="card-icon">📋</div>
                <div class="card-value">${Object.keys(projectStats).length}</div>
                <div class="card-label">项目数量</div>
            </div>
            <div class="card">
                <div class="card-icon">📈</div>
                <div class="card-value">${(totalAllTime / Object.keys(stats).length).toFixed(1)}</div>
                <div class="card-label">人均工时（小时）</div>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2 class="section-title">
                    <span>📊</span>
                    个人工时统计
                </h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="userChart"></canvas>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>姓名</th>
                                <th>总工时</th>
                                <th>参与项目数</th>
                                <th>工时占比</th>
                                <th>工时分布</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sortedUsers.map((userName, index) => {
        const userTotalTime = Object.values(stats[userName]).reduce((sum, project) => sum + project.totalTime, 0);
        const projectCount = Object.keys(stats[userName]).length;
        const percentage = (userTotalTime / totalAllTime * 100);
        return `
                                <tr>
                                    <td><span class="rank-badge">${index + 1}</span></td>
                                    <td><strong>${userName}</strong></td>
                                    <td>${userTotalTime.toFixed(1)} 小时</td>
                                    <td>${projectCount} 个</td>
                                    <td>${percentage.toFixed(1)}%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: ${percentage}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                `;
    }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">
                    <span>🚀</span>
                    项目工时统计
                </h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="projectChart"></canvas>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>项目名称</th>
                                <th>总工时</th>
                                <th>参与人数</th>
                                <th>任务数</th>
                                <th>参与人员</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sortedProjects.slice(0, 20).map((projectName, index) => {
        const project = projectStats[projectName];
        return `
                                <tr>
                                    <td><span class="rank-badge">${index + 1}</span></td>
                                    <td><strong>${projectName}</strong></td>
                                    <td>${project.totalTime.toFixed(1)} 小时</td>
                                    <td>${project.participants.length} 人</td>
                                    <td>${project.taskCount} 个</td>
                                    <td>
                                        <div class="project-participants">
                                            ${project.participants
                .sort((a, b) => b.time - a.time)
                .slice(0, 5)
                .map(p => `<span class="participant-tag">${p.name} (${p.time.toFixed(1)}h)</span>`)
                .join('')}
                                            ${project.participants.length > 5 ? '<span class="participant-tag">...</span>' : ''}
                                        </div>
                                    </td>
                                </tr>
                                `;
    }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 个人工时图表
        const userCtx = document.getElementById('userChart').getContext('2d');
        const userData = ${JSON.stringify(sortedUsers.slice(0, 10).map(userName => {
        const userTotalTime = Object.values(stats[userName]).reduce((sum, project) => sum + project.totalTime, 0);
        return { name: userName, time: userTotalTime };
    }))};

        new Chart(userCtx, {
            type: 'bar',
            data: {
                labels: userData.map(u => u.name),
                datasets: [{
                    label: '工时（小时）',
                    data: userData.map(u => u.time),
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 2,
                    borderRadius: 8,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '个人工时排行榜（前10名）',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '工时（小时）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '人员'
                        }
                    }
                }
            }
        });

        // 项目工时图表
        const projectCtx = document.getElementById('projectChart').getContext('2d');
        const projectData = ${JSON.stringify(sortedProjects.slice(0, 10).map(projectName => {
        return { name: projectName.length > 20 ? projectName.substring(0, 20) + '...' : projectName, time: projectStats[projectName].totalTime };
    }))};

        new Chart(projectCtx, {
            type: 'doughnut',
            data: {
                labels: projectData.map(p => p.name),
                datasets: [{
                    data: projectData.map(p => p.time),
                    backgroundColor: [
                        '#667eea', '#764ba2', '#f093fb', '#f5576c',
                        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                        '#ffecd2', '#fcb69f'
                    ],
                    borderWidth: 3,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    title: {
                        display: true,
                        text: '项目工时分布（前10名）',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>`;

    return html;
}

// 主函数
function main() {
    console.log('🔍 开始分析8月份工时数据...\n');

    const stats = analyzeAugustWorkTime();

    if (Object.keys(stats).length === 0) {
        console.log('❌ 没有找到8月份的数据');
        return;
    }

    // 显示基本统计
    displayResults(stats);

    // 显示项目维度统计
    generateProjectStats(stats);

    // 生成HTML报告
    const htmlContent = generateHTMLReport(stats);
    fs.writeFileSync('worktime_report.html', htmlContent, 'utf8');
    console.log('\n📄 HTML报告已生成: worktime_report.html');

    console.log('\n✅ 分析完成！');
}

// 运行程序
main();
