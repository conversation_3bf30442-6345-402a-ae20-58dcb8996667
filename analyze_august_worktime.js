const fs = require('fs');

// 读取JSON文件
function readDataFile() {
    try {
        const data = fs.readFileSync('data.json', 'utf8');
        const jsonData = JSON.parse(data);
        return jsonData.data || [];
    } catch (error) {
        console.error('读取文件失败:', error);
        return [];
    }
}

// 检查日期是否为8月份
function isAugust(dateString) {
    if (!dateString) return false;
    const date = new Date(dateString);
    return date.getFullYear() === 2025 && date.getMonth() === 7; // 8月份是索引7
}

// 统计8月份每个人各个项目的用时
function analyzeAugustWorkTime() {
    const data = readDataFile();
    
    // 过滤8月份的数据
    const augustData = data.filter(item => {
        return isAugust(item.PlanDate) || isAugust(item.CreateDatetime);
    });
    
    console.log(`总共找到 ${augustData.length} 条8月份的记录`);
    
    // 按人员和项目分组统计
    const stats = {};
    
    augustData.forEach(item => {
        const userName = item.CreateUserName || '未知用户';
        const projectName = item.DevProjectName || '未知项目';
        const workTime = parseFloat(item.WorkTime) || 0;
        
        // 初始化用户数据
        if (!stats[userName]) {
            stats[userName] = {};
        }
        
        // 初始化项目数据
        if (!stats[userName][projectName]) {
            stats[userName][projectName] = {
                totalTime: 0,
                taskCount: 0,
                tasks: []
            };
        }
        
        // 累加工时
        stats[userName][projectName].totalTime += workTime;
        stats[userName][projectName].taskCount += 1;
        stats[userName][projectName].tasks.push({
            title: item.MatterTitle,
            workTime: workTime,
            date: item.PlanDate,
            category: item.MatterCateName
        });
    });
    
    return stats;
}

// 格式化输出结果
function displayResults(stats) {
    console.log('\n=== 8月份工时统计报告 ===\n');
    
    // 按用户排序
    const sortedUsers = Object.keys(stats).sort();
    
    let totalAllTime = 0;
    
    sortedUsers.forEach(userName => {
        console.log(`👤 ${userName}:`);
        console.log('─'.repeat(50));
        
        const userProjects = stats[userName];
        const sortedProjects = Object.keys(userProjects).sort();
        
        let userTotalTime = 0;
        
        sortedProjects.forEach(projectName => {
            const projectData = userProjects[projectName];
            userTotalTime += projectData.totalTime;
            
            console.log(`  📋 ${projectName}`);
            console.log(`     ⏱️  总工时: ${projectData.totalTime.toFixed(1)} 小时`);
            console.log(`     📝 任务数: ${projectData.taskCount} 个`);
            console.log(`     📊 平均工时: ${(projectData.totalTime / projectData.taskCount).toFixed(1)} 小时/任务`);
            console.log('');
        });
        
        console.log(`  🎯 ${userName} 8月总工时: ${userTotalTime.toFixed(1)} 小时`);
        console.log(`  📈 参与项目数: ${sortedProjects.length} 个`);
        console.log('');
        
        totalAllTime += userTotalTime;
    });
    
    console.log('='.repeat(60));
    console.log(`🏆 8月份全员总工时: ${totalAllTime.toFixed(1)} 小时`);
    console.log(`👥 参与人员数: ${sortedUsers.length} 人`);
    console.log(`📊 平均每人工时: ${(totalAllTime / sortedUsers.length).toFixed(1)} 小时`);
}

// 生成详细报表
function generateDetailedReport(stats) {
    console.log('\n=== 详细工时明细 ===\n');
    
    Object.keys(stats).sort().forEach(userName => {
        console.log(`\n📋 ${userName} 详细明细:`);
        console.log('─'.repeat(80));
        
        Object.keys(stats[userName]).sort().forEach(projectName => {
            const projectData = stats[userName][projectName];
            console.log(`\n  🔹 ${projectName} (${projectData.totalTime.toFixed(1)}h):`);
            
            projectData.tasks.forEach(task => {
                console.log(`    • ${task.title} - ${task.workTime}h [${task.category}] (${task.date.split(' ')[0]})`);
            });
        });
    });
}

// 生成项目维度统计
function generateProjectStats(stats) {
    console.log('\n=== 项目维度统计 ===\n');
    
    const projectStats = {};
    
    // 重新组织数据，按项目分组
    Object.keys(stats).forEach(userName => {
        Object.keys(stats[userName]).forEach(projectName => {
            if (!projectStats[projectName]) {
                projectStats[projectName] = {
                    totalTime: 0,
                    participants: [],
                    taskCount: 0
                };
            }
            
            projectStats[projectName].totalTime += stats[userName][projectName].totalTime;
            projectStats[projectName].participants.push({
                name: userName,
                time: stats[userName][projectName].totalTime
            });
            projectStats[projectName].taskCount += stats[userName][projectName].taskCount;
        });
    });
    
    // 按总工时排序项目
    const sortedProjects = Object.keys(projectStats).sort((a, b) => 
        projectStats[b].totalTime - projectStats[a].totalTime
    );
    
    sortedProjects.forEach(projectName => {
        const project = projectStats[projectName];
        console.log(`🚀 ${projectName}:`);
        console.log(`   ⏱️  总工时: ${project.totalTime.toFixed(1)} 小时`);
        console.log(`   👥 参与人数: ${project.participants.length} 人`);
        console.log(`   📝 任务总数: ${project.taskCount} 个`);
        console.log(`   👨‍💻 参与人员:`);
        
        project.participants
            .sort((a, b) => b.time - a.time)
            .forEach(participant => {
                console.log(`      • ${participant.name}: ${participant.time.toFixed(1)}h`);
            });
        console.log('');
    });
}

// 主函数
function main() {
    console.log('🔍 开始分析8月份工时数据...\n');
    
    const stats = analyzeAugustWorkTime();
    
    if (Object.keys(stats).length === 0) {
        console.log('❌ 没有找到8月份的数据');
        return;
    }
    
    // 显示基本统计
    displayResults(stats);
    
    // 显示项目维度统计
    generateProjectStats(stats);
    
    // 显示详细明细（可选，数据量大时可注释掉）
    // generateDetailedReport(stats);
    
    console.log('\n✅ 分析完成！');
}

// 运行程序
main();
