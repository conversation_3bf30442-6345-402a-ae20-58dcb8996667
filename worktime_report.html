<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8月份工时统计报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 40px;
            background: #f8f9fa;
        }

        .card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .card-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .card-label {
            color: #666;
            font-size: 1.1em;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 50px;
        }

        .section-title {
            font-size: 2em;
            color: #333;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: left;
            font-weight: 600;
        }

        td {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .rank-badge {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }

        .project-participants {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }

        .participant-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        @media (max-width: 768px) {
            .summary-cards {
                grid-template-columns: 1fr;
                padding: 20px;
            }

            .content {
                padding: 20px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 8月份工时统计报告</h1>
            <div class="subtitle">2025年8月团队工作量分析</div>
        </div>

        <div class="summary-cards">
            <div class="card">
                <div class="card-icon">⏱️</div>
                <div class="card-value">2216.2</div>
                <div class="card-label">总工时（小时）</div>
            </div>
            <div class="card">
                <div class="card-icon">👥</div>
                <div class="card-value">15</div>
                <div class="card-label">参与人员</div>
            </div>
            <div class="card">
                <div class="card-icon">📋</div>
                <div class="card-value">60</div>
                <div class="card-label">项目数量</div>
            </div>
            <div class="card">
                <div class="card-icon">📈</div>
                <div class="card-value">147.7</div>
                <div class="card-label">人均工时（小时）</div>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2 class="section-title">
                    <span>📊</span>
                    个人工时统计
                </h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="userChart"></canvas>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>姓名</th>
                                <th>总工时</th>
                                <th>参与项目数</th>
                                <th>工时占比</th>
                                <th>工时分布</th>
                            </tr>
                        </thead>
                        <tbody>
                            
                                <tr>
                                    <td><span class="rank-badge">1</span></td>
                                    <td><strong>顾健</strong></td>
                                    <td>209.1 小时</td>
                                    <td>6 个</td>
                                    <td>9.4%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 9.435069037090516%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">2</span></td>
                                    <td><strong>王建文</strong></td>
                                    <td>182.1 小时</td>
                                    <td>13 个</td>
                                    <td>8.2%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 8.216767439761755%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">3</span></td>
                                    <td><strong>仲保福</strong></td>
                                    <td>181.5 小时</td>
                                    <td>7 个</td>
                                    <td>8.2%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 8.189694070932227%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">4</span></td>
                                    <td><strong>赵倩倩</strong></td>
                                    <td>181.1 小时</td>
                                    <td>8 个</td>
                                    <td>8.2%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 8.171645158379208%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">5</span></td>
                                    <td><strong>陈永立</strong></td>
                                    <td>177.5 小时</td>
                                    <td>9 个</td>
                                    <td>8.0%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 8.00920494540204%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">6</span></td>
                                    <td><strong>王林</strong></td>
                                    <td>177.0 小时</td>
                                    <td>12 个</td>
                                    <td>8.0%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 7.986643804710766%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">7</span></td>
                                    <td><strong>钱晓晓</strong></td>
                                    <td>163.0 小时</td>
                                    <td>15 个</td>
                                    <td>7.4%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 7.3549318653551135%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">8</span></td>
                                    <td><strong>薛季飞</strong></td>
                                    <td>162.0 小时</td>
                                    <td>18 个</td>
                                    <td>7.3%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 7.309809583972567%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">9</span></td>
                                    <td><strong>周舟</strong></td>
                                    <td>160.0 小时</td>
                                    <td>16 个</td>
                                    <td>7.2%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 7.219565021207473%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">10</span></td>
                                    <td><strong>侯鑫辉</strong></td>
                                    <td>157.0 小时</td>
                                    <td>11 个</td>
                                    <td>7.1%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 7.084198177059833%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">11</span></td>
                                    <td><strong>王飞越</strong></td>
                                    <td>157.0 小时</td>
                                    <td>13 个</td>
                                    <td>7.1%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 7.084198177059833%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">12</span></td>
                                    <td><strong>朱杰</strong></td>
                                    <td>153.7 小时</td>
                                    <td>14 个</td>
                                    <td>6.9%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 6.935294648497428%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">13</span></td>
                                    <td><strong>张昊</strong></td>
                                    <td>150.0 小时</td>
                                    <td>8 个</td>
                                    <td>6.8%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 6.768342207382005%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">14</span></td>
                                    <td><strong>张晓辉</strong></td>
                                    <td>5.0 小时</td>
                                    <td>1 个</td>
                                    <td>0.2%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0.22561140691273354%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">15</span></td>
                                    <td><strong>郑金国</strong></td>
                                    <td>0.2 小时</td>
                                    <td>2 个</td>
                                    <td>0.0%</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0.009024456276509342%"></div>
                                        </div>
                                    </td>
                                </tr>
                                
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">
                    <span>🚀</span>
                    项目工时统计
                </h2>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <canvas id="projectChart"></canvas>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>项目名称</th>
                                <th>总工时</th>
                                <th>参与人数</th>
                                <th>任务数</th>
                                <th>参与人员</th>
                            </tr>
                        </thead>
                        <tbody>
                            
                                <tr>
                                    <td><span class="rank-badge">1</span></td>
                                    <td><strong>安友销售相关事项【无项目】</strong></td>
                                    <td>291.8 小时</td>
                                    <td>3 人</td>
                                    <td>119 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">张昊 (143.8h)</span><span class="participant-tag">朱杰 (76.0h)</span><span class="participant-tag">仲保福 (72.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">2</span></td>
                                    <td><strong>安友内部相关事项</strong></td>
                                    <td>291.1 小时</td>
                                    <td>12 人</td>
                                    <td>63 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">顾健 (88.0h)</span><span class="participant-tag">王建文 (53.5h)</span><span class="participant-tag">仲保福 (52.0h)</span><span class="participant-tag">陈永立 (23.0h)</span><span class="participant-tag">侯鑫辉 (23.0h)</span>
                                            <span class="participant-tag">...</span>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">3</span></td>
                                    <td><strong>天府江东线缆计算系统</strong></td>
                                    <td>224.1 小时</td>
                                    <td>4 人</td>
                                    <td>34 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">顾健 (104.1h)</span><span class="participant-tag">王林 (88.5h)</span><span class="participant-tag">赵倩倩 (19.5h)</span><span class="participant-tag">王飞越 (12.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">4</span></td>
                                    <td><strong>如皋设备租赁系统</strong></td>
                                    <td>194.0 小时</td>
                                    <td>9 人</td>
                                    <td>41 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">侯鑫辉 (86.0h)</span><span class="participant-tag">薛季飞 (48.5h)</span><span class="participant-tag">王飞越 (18.0h)</span><span class="participant-tag">钱晓晓 (13.0h)</span><span class="participant-tag">朱杰 (10.0h)</span>
                                            <span class="participant-tag">...</span>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">5</span></td>
                                    <td><strong>妙洁物业综合管理平台</strong></td>
                                    <td>181.5 小时</td>
                                    <td>7 人</td>
                                    <td>36 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">赵倩倩 (81.0h)</span><span class="participant-tag">王飞越 (25.0h)</span><span class="participant-tag">钱晓晓 (20.0h)</span><span class="participant-tag">侯鑫辉 (20.0h)</span><span class="participant-tag">薛季飞 (16.0h)</span>
                                            <span class="participant-tag">...</span>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">6</span></td>
                                    <td><strong>【产品】OAMS经营性资产管理系统-通用版</strong></td>
                                    <td>109.0 小时</td>
                                    <td>5 人</td>
                                    <td>22 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">钱晓晓 (67.5h)</span><span class="participant-tag">薛季飞 (22.5h)</span><span class="participant-tag">周舟 (9.0h)</span><span class="participant-tag">王飞越 (8.0h)</span><span class="participant-tag">陈永立 (2.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">7</span></td>
                                    <td><strong>中德船舶管理系统-太仓2.0</strong></td>
                                    <td>92.0 小时</td>
                                    <td>1 人</td>
                                    <td>15 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">陈永立 (92.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">8</span></td>
                                    <td><strong>中燃油漆仓储管理系统</strong></td>
                                    <td>74.0 小时</td>
                                    <td>5 人</td>
                                    <td>15 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">王飞越 (31.0h)</span><span class="participant-tag">王林 (30.0h)</span><span class="participant-tag">周舟 (7.0h)</span><span class="participant-tag">顾健 (4.0h)</span><span class="participant-tag">王建文 (2.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">9</span></td>
                                    <td><strong>安徽天长银行营销考核统计系统</strong></td>
                                    <td>67.0 小时</td>
                                    <td>3 人</td>
                                    <td>18 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">王建文 (37.5h)</span><span class="participant-tag">王林 (21.5h)</span><span class="participant-tag">陈永立 (8.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">10</span></td>
                                    <td><strong>振华RPA实施项目</strong></td>
                                    <td>56.0 小时</td>
                                    <td>3 人</td>
                                    <td>7 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">陈永立 (32.0h)</span><span class="participant-tag">王建文 (16.0h)</span><span class="participant-tag">仲保福 (8.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">11</span></td>
                                    <td><strong>水羊固定资产管理系统</strong></td>
                                    <td>41.9 小时</td>
                                    <td>4 人</td>
                                    <td>14 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">周舟 (30.0h)</span><span class="participant-tag">钱晓晓 (7.5h)</span><span class="participant-tag">顾健 (4.0h)</span><span class="participant-tag">张昊 (0.4h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">12</span></td>
                                    <td><strong>轨道交通物业管理系统</strong></td>
                                    <td>39.0 小时</td>
                                    <td>5 人</td>
                                    <td>8 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">王飞越 (16.0h)</span><span class="participant-tag">薛季飞 (9.5h)</span><span class="participant-tag">周舟 (8.0h)</span><span class="participant-tag">钱晓晓 (3.0h)</span><span class="participant-tag">侯鑫辉 (2.5h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">13</span></td>
                                    <td><strong>复旦大学老龄学院网站</strong></td>
                                    <td>38.6 小时</td>
                                    <td>1 人</td>
                                    <td>7 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">赵倩倩 (38.6h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">14</span></td>
                                    <td><strong>【产品】固定资产管理系统-标准版</strong></td>
                                    <td>32.5 小时</td>
                                    <td>2 人</td>
                                    <td>8 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">钱晓晓 (18.0h)</span><span class="participant-tag">薛季飞 (14.5h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">15</span></td>
                                    <td><strong> 南通国税档案信息管理系统</strong></td>
                                    <td>29.0 小时</td>
                                    <td>1 人</td>
                                    <td>7 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">周舟 (29.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">16</span></td>
                                    <td><strong>安友战略事项</strong></td>
                                    <td>28.9 小时</td>
                                    <td>6 人</td>
                                    <td>15 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">朱杰 (16.0h)</span><span class="participant-tag">薛季飞 (5.0h)</span><span class="participant-tag">顾健 (5.0h)</span><span class="participant-tag">仲保福 (2.0h)</span><span class="participant-tag">张昊 (0.8h)</span>
                                            <span class="participant-tag">...</span>
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">17</span></td>
                                    <td><strong>安徽晓强科技固定资产单机版</strong></td>
                                    <td>28.0 小时</td>
                                    <td>2 人</td>
                                    <td>3 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">仲保福 (24.0h)</span><span class="participant-tag">周舟 (4.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">18</span></td>
                                    <td><strong>南通男子监狱固定资产项目</strong></td>
                                    <td>27.2 小时</td>
                                    <td>5 人</td>
                                    <td>12 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">薛季飞 (11.0h)</span><span class="participant-tag">周舟 (9.0h)</span><span class="participant-tag">钱晓晓 (5.5h)</span><span class="participant-tag">仲保福 (1.0h)</span><span class="participant-tag">张昊 (0.7h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">19</span></td>
                                    <td><strong>中智南通中燃润滑油业务管理系统平台升级改造</strong></td>
                                    <td>27.0 小时</td>
                                    <td>5 人</td>
                                    <td>10 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">陈永立 (12.0h)</span><span class="participant-tag">王建文 (12.0h)</span><span class="participant-tag">钱晓晓 (1.0h)</span><span class="participant-tag">王林 (1.0h)</span><span class="participant-tag">朱杰 (1.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td><span class="rank-badge">20</span></td>
                                    <td><strong>山西长治职业高级中学固定资产项目</strong></td>
                                    <td>25.5 小时</td>
                                    <td>2 人</td>
                                    <td>5 个</td>
                                    <td>
                                        <div class="project-participants">
                                            <span class="participant-tag">仲保福 (22.5h)</span><span class="participant-tag">周舟 (3.0h)</span>
                                            
                                        </div>
                                    </td>
                                </tr>
                                
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 个人工时图表
        const userCtx = document.getElementById('userChart').getContext('2d');
        const userData = [{"name":"顾健","time":209.1},{"name":"王建文","time":182.1},{"name":"仲保福","time":181.5},{"name":"赵倩倩","time":181.1},{"name":"陈永立","time":177.5},{"name":"王林","time":177},{"name":"钱晓晓","time":163},{"name":"薛季飞","time":162},{"name":"周舟","time":160},{"name":"侯鑫辉","time":157}];

        new Chart(userCtx, {
            type: 'bar',
            data: {
                labels: userData.map(u => u.name),
                datasets: [{
                    label: '工时（小时）',
                    data: userData.map(u => u.time),
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 2,
                    borderRadius: 8,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '个人工时排行榜（前10名）',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '工时（小时）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '人员'
                        }
                    }
                }
            }
        });

        // 项目工时图表
        const projectCtx = document.getElementById('projectChart').getContext('2d');
        const projectData = [{"name":"安友销售相关事项【无项目】","time":291.79999999999995},{"name":"安友内部相关事项","time":291.1},{"name":"天府江东线缆计算系统","time":224.1},{"name":"如皋设备租赁系统","time":194},{"name":"妙洁物业综合管理平台","time":181.5},{"name":"【产品】OAMS经营性资产管理系统-通用...","time":109},{"name":"中德船舶管理系统-太仓2.0","time":92},{"name":"中燃油漆仓储管理系统","time":74},{"name":"安徽天长银行营销考核统计系统","time":67},{"name":"振华RPA实施项目","time":56}];

        new Chart(projectCtx, {
            type: 'doughnut',
            data: {
                labels: projectData.map(p => p.name),
                datasets: [{
                    data: projectData.map(p => p.time),
                    backgroundColor: [
                        '#667eea', '#764ba2', '#f093fb', '#f5576c',
                        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                        '#ffecd2', '#fcb69f'
                    ],
                    borderWidth: 3,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    title: {
                        display: true,
                        text: '项目工时分布（前10名）',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>